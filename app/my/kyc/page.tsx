import { Header } from "@/layouts";
import React from "react";
import KYCContent from "./parts/KycContent";
import { cookies } from "next/headers";
import { COOKIES_ACCESS_TOKEN_KEY } from "@/constants/common";
import config from "@/config/index";

async function createAccessToken() {
  const accessToken =
    (await cookies()).get(COOKIES_ACCESS_TOKEN_KEY)?.value || "";

  try {
    const url = `${config.apiUrl}/v1/kyc/verification`;
    const res = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      cache: "no-store",
    });

    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(`Failed to get token: ${errorText}`);
    }

    return await res.json();
  } catch (e: any) {
    console.log("Error creating access token:", e?.message);
  }
}

interface KYCPageProps {
  searchParams: Promise<{
    level?: string;
  }>;
}

export default async function KYCPage({ searchParams }: KYCPageProps) {
  const { level } = await searchParams;
  const kycLevel = level || "basic";
  const data = await createAccessToken();

  return (
    <div>
      <Header />
      <KYCContent accessToken={data?.sumsubAccessToken} kycLevel={kycLevel} />
    </div>
  );
}
