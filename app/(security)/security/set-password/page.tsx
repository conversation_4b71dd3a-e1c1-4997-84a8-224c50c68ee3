"use client";

import React, { useEffect, useState } from "react";
import { ModalEnterVerificationCode } from "@/modals";
import { AppButton } from "@/components";
import { CheckIcon, EyeCloseIcon, EyeIcon } from "@/assets/icons";
import { useRouter } from "next/navigation";

const FromSetPassword = () => {
  const [password, setPassword] = useState<string>("");
  const [passwordConfirm, setPasswordConfirm] = useState<string>("");
  const [isShowPassword, setIsShowPassword] = useState<boolean>(false);
  const [isShowPasswordConfirm, setIsShowPasswordConfirm] =
    useState<boolean>(false);

  const isValidLength = password.length > 7 && password.length < 128;
  const isValidHasNumber = /\d/.test(password);
  const isValidHasUppercase = /[A-Z]/.test(password);

  return (
    <div className="mx-auto flex w-full max-w-[443px] flex-1 flex-col items-center justify-center lg:mt-[50px]">
      <div className="heading-lg-medium-24 mb-4 text-center">
        Enjoy Faster Login
      </div>

      <div className="mt-8 w-full">
        <div className="body-sm-medium-12 text-white-700 mb-2">
          New Password
        </div>
        <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border p-2">
          <input
            type={isShowPassword ? "text" : "password"}
            className="body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          <div
            className="cursor-pointer"
            onClick={() => setIsShowPassword(!isShowPassword)}
          >
            {isShowPassword ? <EyeCloseIcon /> : <EyeIcon />}
          </div>
        </div>
      </div>

      <div className="mt-2 flex w-full flex-col gap-2">
        <div
          className={` flex items-center gap-2 ${
            isValidLength ? "text-brand-500" : "text-white-500"
          }`}
        >
          <CheckIcon />
          <div className="body-md-regular-14">8 to 128 characters</div>
        </div>
        <div
          className={` flex items-center gap-2 ${
            isValidHasNumber ? "text-brand-500" : "text-white-500"
          }`}
        >
          <CheckIcon />
          <div className="body-md-regular-14">At least 1 number</div>
        </div>
        <div
          className={` flex items-center gap-2 ${
            isValidHasUppercase ? "text-brand-500" : "text-white-500"
          }`}
        >
          <CheckIcon />
          <div className="body-md-regular-14">At least 1 upper case letter</div>
        </div>
      </div>

      <div className="mt-8 w-full">
        <div className="body-sm-medium-12 text-white-700 mb-2">
          Confirm Password
        </div>
        <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border p-2">
          <input
            type={isShowPasswordConfirm ? "text" : "password"}
            className="body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none"
            value={passwordConfirm}
            onChange={(e) => setPasswordConfirm(e.target.value)}
          />
          <div
            className="cursor-pointer"
            onClick={() => setIsShowPasswordConfirm(!isShowPasswordConfirm)}
          >
            {isShowPasswordConfirm ? <EyeCloseIcon /> : <EyeIcon />}
          </div>
        </div>
      </div>

      <AppButton size="large" variant="buy" className="mt-8 w-full">
        Confirm
      </AppButton>
    </div>
  );
};

const SetPasswordPage = () => {
  const [isShowModalEnterCode, setIsShowModalEnterCode] =
    useState<boolean>(false);
  const [isShowChangePassword, setIsShowChangePassword] =
    useState<boolean>(true);
  const router = useRouter();

  useEffect(() => {
    setIsShowModalEnterCode(false);
  }, []);

  return (
    <div className="mt-4">
      <div className="heading-lg-medium-24 mb-4 hidden md:block">
        Set Password
      </div>

      {isShowChangePassword && <FromSetPassword />}

      {isShowModalEnterCode && (
        <ModalEnterVerificationCode
          isOpen={isShowModalEnterCode}
          onClose={() => {
            setIsShowModalEnterCode(false);
            router.push("/my/security");
          }}
          onNext={() => {
            setIsShowModalEnterCode(false);
            setIsShowChangePassword(true);
          }}
        />
      )}
    </div>
  );
};

export default SetPasswordPage;
