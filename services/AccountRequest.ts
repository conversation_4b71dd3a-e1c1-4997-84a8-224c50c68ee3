import config from "@/config";
import BaseRootRequest from "./BaseRequest";
import { TChangePasswordParams, TSetPasswordParams } from "@/types/auth";

export default class AccountRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  deposit(params: { networkId: number; asset: string }) {
    const url = `/v1/deposit-address`;
    return this.post(url, params);
  }

  withdraw(params: {
    amount: number;
    asset: string;
    toAddress?: string | null;
    networkId: number | null;
    userId?: string | number | null;
    toUserEmail?: string | null;
  }) {
    const url = `/v1/withdraw`;
    return this.post(url, params);
  }

  getAllWithdrawLimits() {
    const url = `/v1/withdraw-limits`;
    return this.get(url);
  }

  getUserWithdrawLimit() {
    const url = `/v1/user-withdraw-limit`;
    return this.get(url);
  }

  verifyWithdrawCode(params: { code: string; withdrawId: number }) {
    const url = `/v1/verify-withdraw-code`;
    return this.post(url, params);
  }

  getDepositTransactions(params: any) {
    const url = `/v1/deposit-transactions`;
    return this.get(url, params);
  }

  getWithdrawTransactions(params: any) {
    const url = `/v1/withdraw-transactions`;
    return this.get(url, params);
  }

  getTransactionHistories(params: any) {
    const url = `/v1/transaction-histories`;
    return this.get(url, params);
  }

  getProfile(params: any) {
    const url = `/v1/profile`;
    return this.get(url, params);
  }

  getAccount = (omitZeroBalances = true) => {
    const url = `/v1/account`;
    return this.get(url, { omitZeroBalances });
  };

  getAccountActivities = (params: any) => {
    const url = `/v1/account-activities`;
    return this.get(url, params);
  };

  checkUserExits = (params: { userId?: number; email?: string }) => {
    const url = `/v1/user-exist`;
    return this.get(url, params);
  };

  getUserProfile() {
    const url = `/v1/profile`;
    return this.get(url);
  }

  requestSendVerificationCode(id: number) {
    const url = `/v1/account-auth/request-send-verification-code`;
    return this.post(url, { id });
  }

  resendVerificationCode(id: number) {
    const url = `/v1/account-auth/resend-email-verification-code`;
    return this.post(url, { id });
  }

  verifyEmailVerificationCode(params: { id: number; code: string }) {
    const url = `/v1/account-auth/verify-email-verification-code`;
    return this.post(url, params);
  }

  verifyGoogleAuthCode(params: { id: number; code: string }) {
    const url = `/v1/account-auth/verify-google-auth-code`;
    return this.post(url, params);
  }

  getLastLogin() {
    const url = `/v1/last-login`;
    return this.get(url);
  }

  changePassword(params: TChangePasswordParams) {
    const url = `/v1/password/change`;
    return this.post(url, params);
  }

  setPassword(params: TSetPasswordParams) {
    const url = `/v1/password/initialize`;
    return this.post(url, params);
  }
}
