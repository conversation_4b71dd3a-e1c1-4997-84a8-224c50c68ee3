"use client";

import React, { useEffect, useState } from "react";
import { AppButton } from "@/components";
import {
  CloseCircleIcon,
  LoginPasswordIcon,
  EmailIcon,
  AuthenticatorAppIcon,
  Checked,
} from "@/assets/icons";
import { maskEmail } from "@/utils/format";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import Link from "next/link";
import rf from "@/services/RequestFactory";

type TLoginMethod = {
  email: string;
  loginMethods: string[];
  userId: string;
  usesPassword: boolean;
};

const TwoFactorAuthentication = () => {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const securityCheckupState = useSelector(
    (state: RootState) => state.user.securityCheckupState
  );
  console.log(userInfo, "userInfo");
  const [loginMethods, setLoginMethods] = useState<TLoginMethod | null>(null);
  console.log(loginMethods, "loginMethods");

  useEffect(() => {
    const fetchLoginMethods = async () => {
      const res = await rf.getRequest("LoginMethodRequest").getLoginMethods();
      setLoginMethods(res);
    };
    fetchLoginMethods();
  }, []);

  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 lg:text-white-500 text-[16px] lg:text-[24px]">
        Two-Factor Authentication (2FA)
      </div>
      <div className="mt-5 flex flex-col items-center justify-between gap-3 lg:flex-row">
        <div className="flex gap-4 lg:items-center">
          <AuthenticatorAppIcon />
          <div className="flex-1">
            <div className="heading-sm-medium-16 mb-1 text-[14px] lg:text-[16px]">
              Authenticator App
            </div>
            <div className="body-md-regular-14 text-white-500">
              Use authenticator app to protect your account and transactions.
            </div>
          </div>
        </div>

        <div className="flex w-full justify-between gap-2 lg:w-max lg:items-center">
          {securityCheckupState?.twoFactorAuthentication ? (
            <div className="flex items-center gap-1">
              <Checked />
              <div className="body-sm-medium-12">ON</div>
            </div>
          ) : (
            <div className="flex items-center gap-1">
              <CloseCircleIcon />
              <div className="body-sm-medium-12">OFF</div>
            </div>
          )}
          <Link href={"/security/authenticator-app"}>
            <AppButton variant="secondary" className="w-[80px]">
              Manage
            </AppButton>
          </Link>
        </div>
      </div>

      <div className="border-white-100 my-4 flex flex-col items-center justify-between gap-3 border-t pt-4 lg:flex-row lg:border-0">
        <div className="flex gap-4 lg:items-center">
          <EmailIcon />
          <div className="flex-1">
            <div className="heading-sm-medium-16 mb-1 text-[14px] lg:text-[16px]">
              Email
            </div>
            <div className="body-md-regular-14 text-white-500">
              Use your email to protect your account and transactions
            </div>
          </div>
        </div>
        <div className="flex w-full justify-between gap-2 lg:w-max lg:items-center">
          <div>
            <div className="body-sm-medium-12">
              {maskEmail(userInfo.email || "")}
            </div>
          </div>
          <Link href={"/security/email-verification"}>
            {/* <AppButton variant="secondary" className="w-[80px]">
              Manage
            </AppButton> */}
          </Link>
        </div>
      </div>

      <div className="border-white-100 flex flex-col items-center justify-between gap-3 border-t pt-4 lg:flex-row lg:border-0">
        <div className="flex gap-4 lg:items-center">
          <LoginPasswordIcon />
          <div>
            <div className="heading-sm-medium-16 mb-1 text-[14px] lg:text-[16px]">
              Login Password
            </div>
            <div className="body-md-regular-14 text-white-500">
              Login password is used to log in to your account.
            </div>
          </div>
        </div>
        <div className="flex w-full justify-between gap-2 lg:w-max lg:items-center">
          {false ? (
            <div className="flex-col items-center justify-center">
              <div className="flex items-center justify-end gap-1">
                <Checked />
                <div className="body-sm-medium-12">ON</div>
              </div>
              <Link
                href={"/security/change-password"}
                className="body-sm-medium-12 text-green-500"
              >
                Change Password
              </Link>
            </div>
          ) : (
            <div className="flex-col items-center justify-center">
              <div className="flex items-center justify-end gap-1">
                <CloseCircleIcon />
                <div className="body-sm-medium-12">OFF</div>
              </div>
              <Link
                href={"/security/set-password"}
                className="body-sm-medium-12 text-green-500"
              >
                Set Password
              </Link>
            </div>
          )}
          {/* <div className="flex items-center gap-1">
            <CloseCircleIcon />
            <div className="body-sm-medium-12">OFF</div>
          </div>
          <Link href={"/security/change-password"}>
            <AppButton variant="secondary" className="w-[80px]">
              Manage
            </AppButton>
          </Link> */}
        </div>
      </div>
    </div>
  );
};

export default TwoFactorAuthentication;
